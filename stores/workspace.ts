import { doc, onSnapshot } from 'firebase/firestore'
import { defineStore } from 'pinia'

const { getWorkspace, getBilling, getGdpListingLeaderboard, getCounts, getViews, getSmartSpaces } = useApi()

export const useWorkspaceStore = defineStore('workspace', {
  state: (
  ) => ({
    workspace: null as any | null,
    billing: null as any | null,
    profile: null as any | null,
    unsubscribers: [] as any[],
    counts: null as any | null,
    views: null as any | null,
    smartSpaces: null as any | null,
    listingLeaderboard: null as any | null,
  }),
  actions: {
    annihilate() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe())
      this.unsubscribers = []
      this.workspace = null
      this.profile = null
    },
    async init() {
      const { $db } = useNuxtApp()
      if (!this.workspace || !this.billing) {
        const [workspace, billing] = await Promise.all([
          this.workspace ? Promise.resolve(this.workspace) : getWorkspace(),
          this.billing ? Promise.resolve(this.billing) : getBilling(),
        ])
        this.workspace = workspace
        this.billing = billing
      }
      if (!this.profile) {
        const db = $db
        const user = await getCurrentUser()
        try {
          const unsubscribe = onSnapshot(
            doc(db, 'users', user.uid),
            { includeMetadataChanges: true },
            (userDoc) => {
              this.profile = { ...userDoc.data(), id: userDoc.id }
            },
          )
          this.unsubscribers.push(unsubscribe)
        }
        catch (e) {
        }
      }
    },
    async loadBilling() {
      const billing = await getBilling()
      this.billing = billing
    },
    async loadListingLeaderboard() {
      const toast = useToast()
      try {
        this.listingLeaderboard = await getGdpListingLeaderboard()
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load listing leaderboard',
          color: 'error',
        })
      }
    },
    async loadCounts() {
      const toast = useToast()
      try {
        const { data } = await getCounts()
        this.counts = data
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load GDP counts',
          color: 'error',
        })
      }
    },
    async loadViews() {
      const toast = useToast()
      try {
        const { data } = await getViews()
        this.views = data
        console.log('GDP views loaded:', this.views)
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load views',
          color: 'error',
        })
      }
    },
    async loadSmartSpaces() {
      const toast = useToast()
      try {
        const { data } = await getSmartSpaces()
        this.workspace.smartSpaces = data
        console.log('GDP smart spaces loaded:', this.workspace.smartSpaces)
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load smart spaces',
          color: 'error',
        })
      }
    }
  },
})
