import { firestore } from '~/helpers/firebase'

export async function getUser(userId: string) {
  const userSnap = await firestore.doc(`users/${userId}`).get()
  if (!userSnap.exists) {
    return
  }
  return { ...userSnap.data(), id: userSnap.id }
}

export async function getAdminUser(userId: string) {
  let userSnap: any = await firestore.doc(`users/${userId}`).get()
  if (!userSnap.exists) {
    return
  }
  if (userSnap.data().type === 'admin') {
    return { ...userSnap.data(), id: userSnap.id }
  }
  userSnap = await firestore.doc(`users/${userSnap.data().adminId}`).get()
  if (!userSnap.exists) {
    return
  }
  return { ...userSnap.data(), id: userSnap.id }
}

export async function getAgents(userId: string) {
  const agentsSnap = await
  firestore
    .collection('users')
    .where('adminId', '==', userId)
    .get()

  return agentsSnap.docs.map(doc => ({ ...doc.data(), id: doc.id }))
}

export async function getListing(listingId: string) {
  const listingSnap = await firestore.doc(`listings/${listingId}`).get()
  if (!listingSnap.exists) {
    return
  }
  return { ...listingSnap.data(), id: listingSnap.id }
}
