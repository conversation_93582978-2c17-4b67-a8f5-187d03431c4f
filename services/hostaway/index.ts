import axios from 'axios'

class Hostaway {
    private accessToken: string
    private apiOptions: object
    private healthy: boolean

    constructor(accessToken: string) {
        this.accessToken = accessToken
        this.apiOptions = {
            baseURL: 'https://api.hostaway.com/v1',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': `${accessToken}`,
            },
        }
        this.healthy = false
    }

    public async heartbeat(): Promise<boolean> {
        try {
            await axios.get('users?provider=yada-ai', this.apiOptions)
            this.healthy = true
            return true
        }
        catch (e) {
            console.warn('Unable to interact with hostaway API', { error: e })
            return false
        }
    }

    public async getAllListings(): Promise<any[]> {
        const listings: any[] = []
        let skipNum = 0
        const limit = 100
        let skip = true

        while (skip) {
            try {
                const response = await axios.get(
                    `listings?includeResources=1&limit=${limit}&offset=${skipNum}&provider=yada-ai`,
                    this.apiOptions,
                )
                const listingsData = response.data
                console.log(`GOT ${listingsData.result.length} LISTINGS`)
                for (let i = 0; i < listingsData.result.length; i++) {
                    listings.push(listingsData.result[i])
                }
                if (listingsData.result.length !== limit) {
                    console.log('No more listings to get after this batch')
                    skip = false
                }
                else {
                    console.log('More listings to get')
                    skipNum += limit
                }
            }
            catch (e) {
                console.error('Unable to retrieve Hostaway Listings', { error: e })
                skip = false
            }
        }
        return listings
    }

    public async getListing(listingId: string): Promise<any> {
        try {
            const response = await axios.get(`listings/${listingId}?provider=yada-ai`, this.apiOptions)
            console.info('retrieved hostaway listing', { l: response.data })
            return response.data.result
        }
        catch (e) {
            console.error('Unable to retrieve Hostaway listing', { error: e })
            return null
        }
    }

    public async getTemplateMessages(listingId: string): Promise<any[]> {
        try {
            const response = await axios.get(
                `messageTemplates?listingMapId=${listingId}&provider=yada-ai`,
                this.apiOptions,
            )
            return response.data.result
        }
        catch (e) {
            console.error('Unable to retrieve Hostaway message templates', { error: e })
            return []
        }
    }

    public async sendMessage(conversationId: string, body: string, sender: string): Promise<any> {
        console.log('sending a message')
        console.info('sending hostaway message', {
            c: conversationId,
            b: body,
            s: sender,
        })

        let conversation: any
        try {
            const res = await axios.get(
                `conversations/${conversationId}?includeResources=1&provider=yada-ai`,
                this.apiOptions,
            )
            conversation = res.data.result
        }
        catch (e) {
            console.warn('unable to get hostaway conversation', { error: e })
        }

        console.info('retrieved hostaway conversation', { c: conversation })

        let communicationType = conversation.conversationMessages[0].communicationType || null
        if (!communicationType) {
            console.warn('unable to find communicationType')
            let messages
            try {
                messages = await axios.get(
                    `conversations/${conversationId}/messages?provider=yada-ai`,
                    this.apiOptions,
                )
                messages = messages.result
                for (const m of messages) {
                    communicationType = m.communicationType
                }
            }
            catch (e) {
                console.error('unable to retrieve hostaway conversation messages', {
                    error: e,
                })
            }
        }

        console.info('final communication type', { ct: communicationType })
        const data = {
            body,
            communicationType,
        }
        let sentMessage: any
    }
}

export { Hostaway }
