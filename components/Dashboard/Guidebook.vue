<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { useListingStore, useWorkspaceStore } from '#imports'
import { computed, ref } from 'vue'

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const categories: Record<string, BulletLegendItemInterface> = {
  views: { name: 'Guidebook Views', color: '#3b82f6' },
}

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'name',
    header: 'Property Name',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb;',
      }, row.original.name)
    },
  },
  {
    accessorKey: 'views',
    header: 'Views',
    cell: ({ row }) => row.original.views.toString(),
  },
]

const stats = computed(() => {
  if (!workspaceStore.counts)
    return []

  const totalViews = {
    current: workspaceStore.counts[0].guidebook_view_count_last_30_days,
    previous: workspaceStore.counts[0].guidebook_view_count_prev_30_days,
  }

  const percentageChange = calculatePercentageChange(totalViews.current, totalViews.previous)
  const formatChange = (change) => {
    return typeof change === 'number' ? `${change.toFixed(1)}% change` : `${change} growth`
  }

  return [
    {
      title: 'Listings',
      value: workspaceStore.counts[0].unique_listing_count_last_30_days.toString(),
      change: calculatePercentageChange(
        workspaceStore.counts[0].unique_listing_count_last_30_days,
        workspaceStore.counts[0].unique_listing_count_prev_30_days,
      )
        ? formatChange(calculatePercentageChange(
          workspaceStore.counts[0].unique_listing_count_last_30_days,
          workspaceStore.counts[0].unique_listing_count_prev_30_days,
        ))
        : undefined,
      icon: 'pi pi-building',
    },
    {
      title: 'Views',
      value: totalViews.current.toString(),
      change: percentageChange ? formatChange(percentageChange) : undefined,
      icon: 'pi pi-eye',
    },
    {
      title: 'Visitors',
      value: workspaceStore.counts[0].unique_guidebook_sessions_last_30_days.toString(),
      change: calculatePercentageChange(
        workspaceStore.counts[0].unique_guidebook_sessions_last_30_days,
        workspaceStore.counts[0].unique_guidebook_sessions_prev_30_days,
      )
        ? formatChange(calculatePercentageChange(
          workspaceStore.counts[0].unique_guidebook_sessions_last_30_days,
          workspaceStore.counts[0].unique_guidebook_sessions_prev_30_days,
        ))
        : undefined,
      icon: 'pi pi-user',
    },
  ]
})

const chartData = computed(() => {
  if (!workspaceStore.views)
    return []

  return workspaceStore.views.map((item) => {
    const date = new Date(item.event_date)
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    })

    return {
      date: formattedDate,
      views: item.guidebook_view_count_per_day,
    }
  })
})

const topProperties = computed(() => {
  if (!workspaceStore.listingLeaderboard || !listingStore.listings)
    return []

  return workspaceStore.listingLeaderboard
    .map((item) => {
      const listing = listingStore.listings.find(l => l.id === item.listing_id)
      return {
        id: item.listing_id,
        name: listing?.name || 'Unknown Property',
        views: item.last_30_days_views,
      }
    })
    .slice(0, 5)
})

function calculatePercentageChange(current, previous) {
  if (!previous) {
    return current > 0 ? '∞' : 0
  }
  return ((current - previous) / previous) * 100
}
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Guidebook
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Statistics and insights about your guidebook usage over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1 bg-white">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>

      <BarChart :data="chartData" :height="180" :categories="categories" :y-axis="['views']" :x-num-ticks="7" :radius="4"
        :y-grid-line="false" :x-formatter="(i) => `${chartData[i]?.date}`" :y-formatter="(value) => value.toString()"
        :legend-position="LegendPosition.Top" />

      <UTable :data="topProperties" :columns="columns" />
    </UCard>
  </div>
</template>
