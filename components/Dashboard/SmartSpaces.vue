<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { useListingStore, useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const DonutData = [
  {
    color: '#3b82f6',
    name: 'Blue',
    value: 50,
  },
  {
    color: '#a855f7',
    name: 'Gray',
    value: 20,
  },
  {
    color: '#22c55e',
    name: 'Green',
    value: 30,
  },
]

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'name',
    header: 'Property Name',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb;',
      }, row.original.name)
    },
  },
  {
    accessorKey: 'views',
    header: 'Views',
    cell: ({ row }) => row.original.views.toString(),
  },
]

const data = computed(() => {
  try {
    if (!workspaceStore.smartSpaces) {
      console.log('no smart spaces yet')
      return {
        totalScans: 0,
        scansByHour: [],
        popularCards: [],
        popularListings: [],
        uniqueListings: 0,
      }
    }

    // Get aggregate metrics with null check
    const aggregateMetrics = workspaceStore.smartSpaces.find(item => item?.data_type === 'aggregate_metrics') || {}

    // Get sub source breakdown for pie chart with error handling
    const cardIdBreakdown = (workspaceStore.smartSpaces || [])
      .filter(item => item?.data_type === 'card_id_breakdown' && item?.card_id !== null)
      .map(item => ({
        id: item.card_id || 'unknown',
        scans: Number.parseInt(item.card_id_count) || 0,
        name: (item.card_id || 'Unknown')
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
      }))
      .filter(item => Number.isNaN(item.scans) === false) // Filter out invalid scan counts

    // Get last 10 scans sorted by timestamp with error handling
    const recentScans = (workspaceStore.smartSpaces || [])
      .filter(item => item?.data_type === 'last_10_scans' && item?.__timestamp)
      .sort((a, b) => {
        try {
          return new Date(b.__timestamp) - new Date(a.__timestamp)
        }
        catch {
          return 0
        }
      })
      .slice(0, 10)
      .map((item) => {
        const listing = listingStore.listings?.find(l => l?.id === item.listing_id)
        return {
          name: listing?.name || 'Unknown Listing',
          cardId: item.card_id || 'N/A',
          scans: 1,
          date: new Date(item.__timestamp).toLocaleDateString(),
        }
      })

    return {
      totalScans: Number.parseInt(aggregateMetrics.total_rows) || 0,
      uniqueListings: Number.parseInt(aggregateMetrics.unique_listings) || 0,
      popularCards: cardIdBreakdown,
      popularListings: recentScans,
    }
  }
  catch (err) {
    console.error('Error in data computed property:', err)
    return {
      totalScans: 0,
      uniqueListings: 0,
      popularCards: [],
      popularListings: [],
    }
  }
})

const recentScans = computed(() => data.value.popularListings || [])

const stats = computed(() => {
  try {
    // Get sub source counts with null checks
    const coverScans = workspaceStore.smartSpaces
      ?.find(item => item?.data_type === 'sub_src_breakdown' && item?.sub_src === 'cover')
      ?.sub_src_count || '0'
    const amenityScans = workspaceStore.smartSpaces
      ?.find(item => item?.data_type === 'sub_src_breakdown' && item?.sub_src === 'amenity')
      ?.sub_src_count || '0'

    return [
      {
        title: 'Total Scans',
        value: formatNumber(data.value.totalScans),
        change: 'Last 30 Days',
        icon: 'pi pi-qrcode',
      },
      {
        title: 'Cover Scans',
        value: formatNumber(Number.parseInt(coverScans) || 0),
        change: 'Last 30 Days',
        icon: 'pi pi-image',
      },
      {
        title: 'Amenity Scans',
        value: formatNumber(Number.parseInt(amenityScans) || 0),
        change: 'Last 30 Days',
        icon: 'pi pi-list',
      },
    ]
  }
  catch (e) {
    console.error('Error in metrics computed property:', e)
    return []
  }
})

function formatNumber(num) {
  try {
    return (num || 0).toLocaleString()
  }
  catch (e) {
    console.error('Error formatting number:', e)
    return '0'
  }
}
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Smart Spaces
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Statistics and insights about your smart spaces usage over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1 bg-white">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-2">
        <div class="flex justify-center">
          <DonutChart :data="DonutData.map((i) => i.value)" :height="100" :labels="DonutData" :hide-legend="true"
            :radius="0" />
        </div>
        <div>
          <UTable :data="recentScans" />
        </div>
      </div>
    </UCard>
  </div>
</template>
